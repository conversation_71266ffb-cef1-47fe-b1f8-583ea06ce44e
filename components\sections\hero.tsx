"use client"

import { useEffect, useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, MessageCircle, Users, Award, TrendingUp } from "lucide-react"
import Link from "next/link"

const features = [
  {
    icon: Users,
    label: "500+ Happy Clients",
    description: "Trusted by families across Ahmedabad",
  },
  {
    icon: Award,
    label: "10+ Years Experience",
    description: "Decade of real estate expertise",
  },
  {
    icon: TrendingUp,
    label: "₹100Cr+ Deals Closed",
    description: "Successful property transactions",
  },
]

const animatedWords = ["Property", "Investment", "Dream Home", "Future"]

export default function Hero() {
  const heroRef = useRef<HTMLElement>(null)
  const [currentWordIndex, setCurrentWordIndex] = useState(0)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  // Mouse movement effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      })
    }

    window.addEventListener("mousemove", handleMouseMove)
    return () => window.removeEventListener("mousemove", handleMouseMove)
  }, [])

  // Animated words rotation
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentWordIndex((prev) => (prev + 1) % animatedWords.length)
    }, 2500)

    return () => clearInterval(interval)
  }, [])

  // Scroll animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-in")
          }
        })
      },
      { threshold: 0.1 }
    )

    const elements = heroRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={heroRef} className="relative pt-20 pb-12 sm:pt-24 sm:pb-16 lg:pt-28 lg:pb-20 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
      <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>

      {/* Floating Light Shapes */}
      <div className="absolute inset-0 pointer-events-none">
        <div
          className="absolute w-48 h-48 rounded-full bg-white/10 opacity-30 animate-float-minimal"
          style={{
            top: "20%",
            left: "10%",
            transform: `translate(${mousePosition.x * 0.03}px, ${mousePosition.y * 0.03}px)`,
          }}
        ></div>
        <div
          className="absolute w-32 h-32 rounded-full bg-white/15 opacity-40 animate-float-minimal-reverse"
          style={{
            top: "60%",
            right: "15%",
            transform: `translate(${mousePosition.x * -0.02}px, ${mousePosition.y * -0.02}px)`,
          }}
        ></div>
        <div
          className="absolute w-24 h-24 rounded-full bg-white/10 opacity-20 animate-float-minimal"
          style={{
            top: "40%",
            right: "20%",
            transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`,
          }}
        ></div>
        <div
          className="absolute w-40 h-40 rounded-full bg-white/10 opacity-35 animate-float-minimal-reverse"
          style={{
            bottom: "5%",
            left: "25%",
            transform: `translate(${mousePosition.x * -0.04}px, ${mousePosition.y * -0.04}px)`,
          }}
        ></div>
      </div>

      {/* Main Hero Content */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="pb-20">
          {/* Hero Header */}
          <div className="text-center mb-20">
            {/* Badge */}
            <div className="animate-on-scroll opacity-0 translate-y-8 mb-8">
              <div className="inline-flex items-center px-3 sm:px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white text-xs sm:text-sm font-medium">
                <span className="w-2 h-2 bg-green-400 rounded-full mr-2 sm:mr-3 animate-pulse"></span>
                <span className="hidden sm:inline">Premium Real Estate Services</span>
                <span className="sm:hidden">Premium Services</span>
              </div>
            </div>

            {/* Main Headline with Animated Text */}
            <div className="animate-on-scroll opacity-0 translate-y-8 delay-200 mb-8 text-center px-2">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight text-white mb-6">
                <div className="block mb-4 sm:mb-6">Your Trusted</div>
                <div className="block mb-4 sm:mb-6 relative overflow-hidden" style={{ height: 'auto', minHeight: '60px' }}>
                  <div className="rotating-text-container relative flex items-center justify-center w-full">
                    {animatedWords.map((word, index) => (
                      <span
                        key={word}
                        className={`rotating-word absolute transition-all duration-700 ease-in-out transform text-white ${
                          index === currentWordIndex
                            ? 'opacity-100 translate-y-0 scale-100'
                            : 'opacity-0 translate-y-4 scale-85'
                        }`}
                        style={{
                          fontSize: 'clamp(2rem, 8vw, 4rem)',
                          fontWeight: '800',
                          textShadow: '0 0 15px rgba(255, 255, 255, 0.3), 0 2px 4px rgba(0, 0, 0, 0.3)',
                          filter: 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.2))',
                          zIndex: 10,
                          textAlign: 'center',
                          whiteSpace: 'nowrap',
                          left: '50%',
                          top: '50%',
                          transform: `translate(-50%, -50%) ${index === currentWordIndex ? 'scale(1)' : 'translateY(1rem) scale(0.85)'}`,
                          maxWidth: '90%',
                        }}
                      >
                        {word}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="block">Partner</div>
              </h1>
            </div>

            {/* Subtitle */}
            <div className="animate-on-scroll opacity-0 translate-y-8 delay-600 mb-12 px-4">
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-white/90 leading-relaxed max-w-3xl mx-auto font-light">
                Experience luxury living redefined with our comprehensive real estate services. From finding your dream
                home to smart investments, we're your trusted partner in Ahmedabad.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="animate-on-scroll opacity-0 translate-y-8 delay-800 mb-16 px-4">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-white text-burgundy-600 hover:bg-gray-100 px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 group border-0 hover:scale-105 transform text-sm sm:text-base"
                  asChild
                >
                  <Link href="/projects">
                    <span>Explore Properties</span>
                    <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={18} />
                  </Link>
                </Button>

                <Button
                  variant="outline"
                  size="lg"
                  className="border-2 border-white text-white hover:border-white hover:text-burgundy-600 hover:bg-white px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold transition-all duration-300 group hover:scale-105 transform bg-transparent text-sm sm:text-base"
                  asChild
                >
                  <Link href="/contact">
                    <MessageCircle className="mr-2 group-hover:scale-110 transition-transform" size={18} />
                    <span>Get Consultation</span>
                  </Link>
                </Button>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="animate-on-scroll opacity-0 translate-y-8 delay-1000 px-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6 max-w-5xl mx-auto">
                {features.map((feature) => {
                  const IconComponent = feature.icon
                  return (
                    <div key={feature.label} className="group">
                      <div className="bg-white rounded-2xl p-6 sm:p-8 shadow-sm border border-gray-100 hover:shadow-xl hover:border-burgundy-200 transition-all duration-500 hover:-translate-y-2 h-44 sm:h-48 flex flex-col justify-center items-center text-center">
                        <div className="w-12 h-12 sm:w-16 sm:h-16 bg-burgundy-100 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-burgundy-200 group-hover:scale-110 transition-all duration-300">
                          <IconComponent className="text-burgundy-600" size={24} />
                        </div>
                        <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-1 sm:mb-2">{feature.label}</h3>
                        <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">{feature.description}</p>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
